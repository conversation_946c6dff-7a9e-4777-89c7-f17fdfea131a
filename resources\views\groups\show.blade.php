<x-unilink-layout>
    <!-- Group Header -->
    <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden mb-6">
        <!-- Cover Image -->
        <div class="h-48 bg-gradient-to-r from-blue-500 to-purple-600 relative">
            @if($group->cover_image)
                <img src="{{ Storage::disk('public')->url($group->cover_image) }}" alt="{{ $group->name }}" class="w-full h-full object-cover">
            @endif
            
            <!-- Action Buttons -->
            <div class="absolute top-4 right-4 flex space-x-2">
                <livewire:group-membership :group="$group" />

                @if(auth()->check() && $group->userCanModerate(auth()->user()))
                    <a href="{{ route('groups.edit', $group) }}" class="bg-gray-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-gray-700">
                        Edit
                    </a>
                @endif
            </div>
        </div>

        <!-- Group Info -->
        <div class="p-6">
            <div class="flex items-start space-x-4">
                <!-- Logo -->
                <div class="flex-shrink-0">
                    <div class="w-20 h-20 bg-white rounded-lg shadow-md flex items-center justify-center -mt-10 border-4 border-white">
                        @if($group->logo)
                            <img src="{{ Storage::disk('public')->url($group->logo) }}" alt="{{ $group->name }}" class="w-16 h-16 rounded-lg object-cover">
                        @else
                            <div class="w-16 h-16 bg-blue-100 rounded-lg flex items-center justify-center">
                                <span class="text-blue-600 font-bold text-lg">{{ substr($group->name, 0, 2) }}</span>
                            </div>
                        @endif
                    </div>
                </div>

                <!-- Group Details -->
                <div class="flex-1 min-w-0">
                    <div class="flex items-center justify-between">
                        <h1 class="text-2xl font-bold text-gray-900">{{ $group->name }}</h1>
                        <div class="flex space-x-2">
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $group->visibility === 'public' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800' }}">
                                {{ ucfirst($group->visibility) }}
                            </span>
                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $group->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                {{ ucfirst($group->status) }}
                            </span>
                        </div>
                    </div>
                    
                    <p class="text-gray-600 mt-2">{{ $group->description }}</p>
                    
                    @if($group->organization)
                        <div class="mt-2">
                            <span class="text-sm text-gray-500">Part of </span>
                            <a href="{{ route('organizations.show', $group->organization) }}" class="text-sm text-blue-600 hover:text-blue-800">
                                {{ $group->organization->name }}
                            </a>
                        </div>
                    @endif
                    
                    <!-- Stats -->
                    <div class="flex items-center space-x-6 mt-4 text-sm text-gray-500">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            {{ is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count() }} members
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                            Created by {{ $group->creator->name }}
                        </div>
                        <div class="flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3a1 1 0 011-1h6a1 1 0 011 1v4h3a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h3z" />
                            </svg>
                            {{ $group->created_at->format('M Y') }}
                        </div>
                        @if($group->allow_file_sharing)
                            <div class="flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                </svg>
                                File sharing enabled
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Main Content -->
        <div class="lg:col-span-2 space-y-6">
            <!-- Post Creation (for members) -->
            @if($userMembership && $userMembership->pivot->status === 'active')
                <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
                    <div class="flex items-center space-x-3">
                        <img class="h-10 w-10 rounded-full" src="{{ auth()->user()->avatar ? Storage::disk('public')->url(auth()->user()->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7F9CF5&background=EBF4FF' }}" alt="{{ auth()->user()->name }}">
                        <button onclick="openCreatePostModal()" class="flex-1 text-left px-4 py-2 bg-gray-100 rounded-full text-gray-500 hover:bg-gray-200 transition-colors">
                            Share something with the group...
                        </button>
                        @if($group->allow_file_sharing)
                            <button onclick="openCreatePostModal()" class="p-2 text-gray-500 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors" title="Share files">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.172 7l-6.586 6.586a2 2 0 102.828 2.828l6.414-6.586a4 4 0 00-5.656-5.656l-6.415 6.585a6 6 0 108.486 8.486L20.5 13" />
                                </svg>
                            </button>
                        @endif
                        <button onclick="openCreatePostModal()" class="p-2 text-gray-500 hover:text-green-600 hover:bg-green-50 rounded-lg transition-colors" title="Add images">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                        </button>
                    </div>
                </div>
            @endif

            <!-- Recent Posts -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200 flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-gray-900">Recent Posts</h2>
                    @if(auth()->check() && $group->userCanModerate(auth()->user()) && $group->post_approval === 'required')
                        <a href="{{ route('groups.pending-posts', $group) }}" class="text-sm text-blue-600 hover:text-blue-800">
                            Pending Posts
                        </a>
                    @endif
                </div>
                
                @if($group->approvedPosts->count() > 0)
                    <div class="space-y-6">
                        @foreach($group->approvedPosts as $post)
                            <x-post-card :post="$post" />
                        @endforeach
                    </div>
                @else
                    <div class="p-8 text-center">
                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-gray-900">No posts yet</h3>
                        <p class="mt-1 text-sm text-gray-500">Be the first to share something with the group!</p>
                    </div>
                @endif
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-6">
            <!-- Members -->
            <div class="bg-white rounded-lg shadow-sm border border-gray-200">
                <div class="p-4 border-b border-gray-200 flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">Members ({{ is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count() }})</h3>
                    @if(auth()->check() && $group->userCanModerate(auth()->user()))
                        <a href="{{ route('groups.members', $group) }}" class="text-sm text-blue-600 hover:text-blue-800">
                            Manage
                        </a>
                    @endif
                </div>
                
                <div class="p-4">
                    <div class="space-y-3">
                        @foreach((is_array($group->activeMembers) ? collect($group->activeMembers) : $group->activeMembers)->take(10) as $member)
                            <div class="flex items-center space-x-3">
                                <img class="h-8 w-8 rounded-full" src="{{ $member->avatar ? Storage::disk('public')->url($member->avatar) : 'https://ui-avatars.com/api/?name=' . urlencode($member->name) . '&color=7F9CF5&background=EBF4FF' }}" alt="{{ $member->name }}">
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-gray-900 truncate">{{ $member->name }}</p>
                                    <p class="text-xs text-gray-500">{{ ucfirst($member->pivot->role) }}</p>
                                </div>
                                @if($member->pivot->role === 'admin')
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-purple-100 text-purple-800">
                                        Admin
                                    </span>
                                @elseif($member->pivot->role === 'moderator')
                                    <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                                        Moderator
                                    </span>
                                @endif
                            </div>
                        @endforeach
                        
                        @if((is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count()) > 10)
                            <div class="text-center pt-2">
                                <a href="{{ route('groups.members', $group) }}" class="text-sm text-blue-600 hover:text-blue-800">
                                    View all {{ is_array($group->activeMembers) ? count($group->activeMembers) : $group->activeMembers->count() }} members
                                </a>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Post Creation Modal -->
    @if($userMembership && $userMembership->pivot->status === 'active')
        <x-post-creation-modal :group="$group" />
    @endif

    <!-- Include Image Modal for post images -->
    @include('components.image-modal')
</x-unilink-layout>
